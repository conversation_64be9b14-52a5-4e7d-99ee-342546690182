"use client";

import { useState, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useSoundStore } from "@/store/useSoundStore";
import { useVoiceModels } from "../hooks/useVoiceModels";
import { Play, Pause } from "lucide-react";
import { cn } from "@/lib/utils";
import * as SliderPrimitive from "@radix-ui/react-slider";

// ZEN风格滑块组件，专门为dark模式优化
const ZenSlider = ({ className, ...props }: React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>) => (
    <SliderPrimitive.Root
        className={cn(
            "relative flex w-full touch-none select-none items-center",
            className
        )}
        {...props}
    >
        <SliderPrimitive.Track className="relative h-1 w-full grow overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
            <SliderPrimitive.Range className="absolute h-full bg-black dark:bg-white" />
        </SliderPrimitive.Track>
        <SliderPrimitive.Thumb className="block h-4 w-4 rounded-full border border-gray-300 dark:border-gray-600 bg-white dark:bg-black ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-gray-400 focus-visible:ring-offset-1 disabled:pointer-events-none disabled:opacity-50 hover:scale-110" />
    </SliderPrimitive.Root>
);

export function ControlPanel() {
    const {
        selectedVoiceModel,
        speed,
        stability,
        similarityBoost,
        styleExaggeration,
        setSpeed,
        setStability,
        setSimilarityBoost,
        setStyleExaggeration,
        resetSettings,
    } = useSoundStore();

    const { availableVoiceModels, selectVoiceModel } = useVoiceModels();
    const [isPlayingPreview, setIsPlayingPreview] = useState(false);

    const handlePlayPreview = useCallback(() => {
        if (!selectedVoiceModel) return;

        setIsPlayingPreview(true);
        // TODO: Implement voice preview playback
        setTimeout(() => {
            setIsPlayingPreview(false);
        }, 2000);
    }, [selectedVoiceModel]);

    return (
        <div className="w-72 bg-white dark:bg-black border-r border-gray-200 dark:border-gray-800 overflow-y-auto">
            <div className="p-6 space-y-8">
                {/* Header */}
                <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 pb-4">
                    <h2 className="text-sm font-normal text-black dark:text-white">Control</h2>
                    <button
                        onClick={resetSettings}
                        className="text-xs text-gray-700 dark:text-gray-300 hover:text-black dark:hover:text-white transition-colors"
                    >
                        Reset
                    </button>
                </div>

                {/* Voice Selection */}
                <div className="space-y-4">
                    <label className="text-xs font-normal text-gray-600 dark:text-gray-300 uppercase tracking-wide">
                        Voice
                    </label>
                    <div className="space-y-3">
                        <Select
                            value={selectedVoiceModel?.id || ""}
                            onValueChange={selectVoiceModel}
                        >
                            <SelectTrigger className="min-h-[2.5rem] bg-transparent border border-gray-200 dark:border-gray-600 text-sm font-light text-black dark:text-white hover:border-gray-300 dark:hover:border-gray-500 transition-colors">
                                <SelectValue placeholder="Select voice" />
                            </SelectTrigger>
                            <SelectContent className="bg-white dark:bg-black border border-gray-200 dark:border-gray-600">
                                {availableVoiceModels.map((model) => (
                                    <SelectItem
                                        key={model.id}
                                        value={model.id}
                                        className="text-sm font-light text-black dark:text-white hover:bg-gray-50 dark:hover:bg-gray-800 py-3"
                                    >
                                        <div className="flex flex-col">
                                            <div className="font-normal">{model.name}</div>
                                            <div className="text-xs text-gray-500 dark:text-gray-400">{model.description}</div>
                                        </div>
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>

                        {selectedVoiceModel && (
                            <button
                                onClick={handlePlayPreview}
                                disabled={isPlayingPreview}
                                className="w-full h-8 flex items-center justify-center space-x-2 border border-gray-200 dark:border-gray-600 text-xs font-light text-black dark:text-white hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors disabled:opacity-50"
                            >
                                {isPlayingPreview ? (
                                    <>
                                        <Pause className="w-3 h-3" />
                                        <span>Playing</span>
                                    </>
                                ) : (
                                    <>
                                        <Play className="w-3 h-3" />
                                        <span>Preview</span>
                                    </>
                                )}
                            </button>
                        )}
                    </div>
                </div>

                {/* Model Selection */}
                <div className="space-y-4">
                    <label className="text-xs font-normal text-gray-600 dark:text-gray-300 uppercase tracking-wide">
                        Model
                    </label>
                    <Select defaultValue="eleven-multilingual-v2">
                        <SelectTrigger className="h-9 bg-transparent border border-gray-200 dark:border-gray-600 text-sm font-light text-black dark:text-white hover:border-gray-300 dark:hover:border-gray-500 transition-colors">
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-white dark:bg-black border border-gray-200 dark:border-gray-600">
                            <SelectItem value="eleven-multilingual-v2" className="text-sm font-light text-black dark:text-white hover:bg-gray-50 dark:hover:bg-gray-800">
                                Eleven Multilingual v2
                            </SelectItem>
                            <SelectItem value="eleven-turbo-v2" className="text-sm font-light text-black dark:text-white hover:bg-gray-50 dark:hover:bg-gray-800">
                                Eleven Turbo v2
                            </SelectItem>
                        </SelectContent>
                    </Select>
                </div>

                {/* Speed Control */}
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <label className="text-xs font-normal text-gray-600 dark:text-gray-300 uppercase tracking-wide">
                            Speed
                        </label>
                        <span className="text-xs text-black dark:text-white font-mono">{speed.toFixed(2)}</span>
                    </div>
                    <div className="space-y-3">
                        <ZenSlider
                            value={[speed]}
                            onValueChange={(value) => setSpeed(value[0])}
                            min={0.25}
                            max={4.0}
                            step={0.05}
                            className="w-full"
                        />
                        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-300 font-mono">
                            <span>0.25</span>
                            <span>4.0</span>
                        </div>
                    </div>
                </div>

                {/* Stability Control */}
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <label className="text-xs font-normal text-gray-600 dark:text-gray-300 uppercase tracking-wide">
                            Stability
                        </label>
                        <span className="text-xs text-black dark:text-white font-mono">{(stability * 100).toFixed(0)}%</span>
                    </div>
                    <div className="space-y-3">
                        <ZenSlider
                            value={[stability]}
                            onValueChange={(value) => setStability(value[0])}
                            min={0}
                            max={1}
                            step={0.01}
                            className="w-full"
                        />
                        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-300">
                            <span>Variable</span>
                            <span>Stable</span>
                        </div>
                    </div>
                </div>

                {/* Similarity Boost Control */}
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <label className="text-xs font-normal text-gray-600 dark:text-gray-300 uppercase tracking-wide">
                            Similarity
                        </label>
                        <span className="text-xs text-black dark:text-white font-mono">{(similarityBoost * 100).toFixed(0)}%</span>
                    </div>
                    <div className="space-y-3">
                        <ZenSlider
                            value={[similarityBoost]}
                            onValueChange={(value) => setSimilarityBoost(value[0])}
                            min={0}
                            max={1}
                            step={0.01}
                            className="w-full"
                        />
                        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-300">
                            <span>Low</span>
                            <span>High</span>
                        </div>
                    </div>
                </div>

                {/* Style Exaggeration Control */}
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <label className="text-xs font-normal text-gray-600 dark:text-gray-300 uppercase tracking-wide">
                            Style
                        </label>
                        <span className="text-xs text-black dark:text-white font-mono">{(styleExaggeration * 100).toFixed(0)}%</span>
                    </div>
                    <div className="space-y-3">
                        <ZenSlider
                            value={[styleExaggeration]}
                            onValueChange={(value) => setStyleExaggeration(value[0])}
                            min={0}
                            max={1}
                            step={0.01}
                            className="w-full"
                        />
                        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-300">
                            <span>None</span>
                            <span>Max</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
